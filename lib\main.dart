import 'package:flutter/material.dart';

void main() {
  runApp(const DCBuyerApp());
}

class DCBuyerApp extends StatelessWidget {
  const DCBuyerApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '<PERSON><PERSON>uyer',
      theme: ThemeData(
        // DCBuyer brand theme with navy blue and green colors
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1B365D), // Navy blue primary
          primary: const Color(0xFF1B365D), // Navy blue
          secondary: const Color(0xFF4CAF50), // Green
          surface: Colors.white,
          background: const Color(0xFFF5F5F5), // Light gray background
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1B365D), // Navy blue app bar
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          backgroundColor: Color(0xFF4CAF50), // Green FAB
          foregroundColor: Colors.white,
        ),
        useMaterial3: true,
      ),
      home: const DCBuyerHomePage(title: '<PERSON>Buyer'),
    );
  }
}

class DCBuyerHomePage extends StatefulWidget {
  const DCBuyerHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<DCBuyerHomePage> createState() => _DCBuyerHomePageState();
}

class _DCBuyerHomePageState extends State<DCBuyerHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset(
              'assets/images/dcb_icon.png',
              height: 32,
              width: 32,
            ),
            const SizedBox(width: 12),
            Text(widget.title),
          ],
        ),
        centerTitle: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            // DCBuyer Logo
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 2,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Image.asset(
                'assets/images/dcb_logo.png',
                height: 120,
                width: 200,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(height: 40),
            Text(
              'Welcome to DCBuyer',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: const Color(0xFF1B365D),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Your trusted agricultural marketplace',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: const Color(0xFF4CAF50),
              ),
            ),
            const SizedBox(height: 40),
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: const Color(0xFF1B365D),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ),
    );
  }
}
