# DCBuyer Firebase System Design Document

## 1. Overview

DCBuyer Firebase-based system is an offline-first commodity trading and purchasing management system leveraging Google Firebase services for backend infrastructure. This design provides serverless architecture with real-time synchronization capabilities.

### 1.1 Purpose
- Leverage Firebase's serverless architecture for rapid development
- Provide real-time data synchronization across devices
- Enable offline-first operations with automatic sync
- Reduce backend infrastructure complexity

### 1.2 Firebase Services Used
- **Firebase Authentication** - User management and authentication
- **Cloud Firestore** - NoSQL document database
- **Firebase Cloud Functions** - Serverless backend logic
- **Firebase Cloud Storage** - File and image storage
- **Firebase Analytics** - User behavior tracking
- **Firebase Cloud Messaging** - Push notifications
- **Firebase Hosting** - Web app hosting

## 2. Firebase Architecture

### 2.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web App       │    │   Admin Panel   │
│   (Flutter)     │    │   (Flutter Web) │    │   (Flutter Web) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Firebase Services                  │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Firebase Auth│  │ Firestore   │  │Cloud Func││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │  GCS Bucket │  │ Analytics   │  │Messaging ││
         │  │(User Files) │  │             │  │          ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Local Storage                      │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   SQLite    │  │    Hive     │  │Local Files││
         │  │(Offline DB) │  │(Key-Value)  │  │(Images)  ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Frontend:**
- Flutter SDK with Firebase plugins
- Provider/Riverpod for state management
- SQLite for offline persistence
- Hive for local key-value storage

**Firebase Backend:**
- Firebase Authentication (Multi-provider support)
- Cloud Firestore (NoSQL document database)
- Firebase Cloud Functions (Node.js/TypeScript)
- Google Cloud Storage (User-uploaded files storage)
- Firebase Analytics & Crashlytics
- Firebase Cloud Messaging (Push notifications)
- Firebase Hosting (Web deployment)

**Development Tools:**
- Firebase CLI for deployment
- FlutterFire CLI for configuration
- Firebase Emulator Suite for local development

## 3. Firestore Database Design

### 3.1 Collection Structure
```
firestore/
├── users/
│   └── {userId}/
│       ├── profile (document)
│       └── roles/ (subcollection)
│           └── {roleId} (document)
├── customers/
│   └── {customerId} (document)
├── commodities/
│   └── {commodityId} (document)
├── assignments/
│   └── {assignmentId} (document)
├── transactions/
│   └── {transactionId} (document)
├── roles/
│   └── {roleId} (document)
└── metadata/
    └── sync (document)
```

### 3.2 Document Schemas

#### Users Collection (`/users/{userId}`)
```json
{
  "userId": "firebase-auth-uid",
  "email": "<EMAIL>",
  "username": "unique_username",
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "profileImageUrl": "gs://bucket/images/profile.jpg"
  },
  "status": {
    "isActive": true,
    "lastLogin": "2024-01-15T10:30:00Z",
    "isOnline": false
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "syncVersion": 5,
    "createdBy": "admin-user-id"
  }
}
```

#### User Roles Subcollection (`/users/{userId}/roles/{roleId}`)
```json
{
  "roleId": "buyer",
  "roleName": "Buyer",
  "permissions": ["read_commodities", "create_transactions"],
  "assignedAt": "2024-01-01T00:00:00Z",
  "assignedBy": "admin-user-id",
  "isActive": true,
  "restrictions": {
    "maxTransactionAmount": 10000,
    "allowedCommodities": ["commodity-1", "commodity-2"]
  }
}
```

#### Customers Collection (`/customers/{customerId}`)
```json
{
  "customerId": "CUST-2024-001",
  "businessId": "unique-business-identifier",
  "profile": {
    "name": "ABC Farm Ltd",
    "contactPerson": "Jane Smith",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "address": {
      "street": "123 Farm Road",
      "city": "Farmville",
      "state": "State",
      "zipCode": "12345",
      "country": "Country"
    }
  },
  "location": {
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "address": "Formatted address string"
  },
  "status": {
    "isActive": true,
    "creditLimit": 50000,
    "paymentTerms": "net-30"
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "createdBy": "user-id",
    "syncVersion": 3
  }
}
```

#### Commodities Collection (`/commodities/{commodityId}`)
```json
{
  "commodityId": "COMM-MAIZE-001",
  "name": "Yellow Maize",
  "description": "Grade A yellow maize for livestock feed",
  "category": "grains",
  "specifications": {
    "grade": "A",
    "moistureContent": "14%",
    "purity": "99%"
  },
  "pricing": {
    "basePrice": 250.00,
    "currency": "USD",
    "unitOfMeasurement": "kg",
    "pricePerUnit": 0.25,
    "lastUpdated": "2024-01-15T00:00:00Z"
  },
  "inventory": {
    "availableQuantity": 10000,
    "reservedQuantity": 500,
    "unit": "kg"
  },
  "status": {
    "isActive": true,
    "isAvailable": true,
    "seasonality": "year-round"
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "syncVersion": 2
  }
}
```

#### Buyer Commodity Assignments (`/assignments/{assignmentId}`)
```json
{
  "assignmentId": "auto-generated-id",
  "buyerId": "user-id",
  "commodityId": "commodity-id",
  "assignment": {
    "assignedAt": "2024-01-01T00:00:00Z",
    "assignedBy": "admin-user-id",
    "isActive": true,
    "territory": "North Region"
  },
  "limits": {
    "maxPurchaseLimit": 100000,
    "dailyLimit": 5000,
    "monthlyLimit": 50000
  },
  "performance": {
    "totalPurchases": 25000,
    "transactionCount": 45,
    "averageTransactionSize": 555.56
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "syncVersion": 1
  }
}
```

#### Transactions Collection (`/transactions/{transactionId}`)
```json
{
  "transactionId": "TXN-2024-001234",
  "transactionNumber": "DCB-20240115-001",
  "parties": {
    "buyerId": "user-id",
    "customerId": "customer-id",
    "buyerName": "John Doe",
    "customerName": "ABC Farm Ltd"
  },
  "commodity": {
    "commodityId": "commodity-id",
    "commodityName": "Yellow Maize",
    "category": "grains"
  },
  "transaction": {
    "quantity": 1000,
    "unit": "kg",
    "unitPrice": 0.25,
    "totalAmount": 250.00,
    "currency": "USD"
  },
  "payment": {
    "amountPaid": 250.00,
    "paymentMethod": "cash",
    "paymentStatus": "completed",
    "paymentDate": "2024-01-15T14:30:00Z",
    "receiptNumber": "RCP-001234"
  },
  "location": {
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "address": "Transaction location"
  },
  "status": {
    "transactionStatus": "completed",
    "approvalStatus": "approved",
    "approvedBy": "supervisor-user-id",
    "approvedAt": "2024-01-15T15:00:00Z"
  },
  "metadata": {
    "transactionDate": "2024-01-15T14:30:00Z",
    "createdAt": "2024-01-15T14:30:00Z",
    "updatedAt": "2024-01-15T15:00:00Z",
    "notes": "Quality grade A confirmed",
    "isSynced": true,
    "syncVersion": 2,
    "deviceId": "device-123",
    "appVersion": "1.0.0"
  }
}
```

#### Roles Collection (`/roles/{roleId}`)
```json
{
  "roleId": "buyer",
  "roleName": "Buyer",
  "description": "Can purchase commodities and manage transactions",
  "permissions": [
    "read_assigned_commodities",
    "create_transactions",
    "read_customers",
    "create_customers",
    "read_own_transactions"
  ],
  "restrictions": {
    "maxTransactionAmount": 10000,
    "requiresApproval": false,
    "canViewAllTransactions": false
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "isSystemRole": true
  }
}
```

## 4. Firebase Security Rules

### 4.1 Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // User roles subcollection
      match /roles/{roleId} {
        allow read: if request.auth != null && request.auth.uid == userId;
        allow write: if request.auth != null && hasRole('admin');
      }
    }
    
    // Customers - buyers can read/create, admins can manage
    match /customers/{customerId} {
      allow read, create: if request.auth != null && hasRole('buyer');
      allow update, delete: if request.auth != null && hasRole('admin');
    }
    
    // Commodities - buyers can read assigned, admins can manage
    match /commodities/{commodityId} {
      allow read: if request.auth != null && canAccessCommodity(commodityId);
      allow write: if request.auth != null && hasRole('admin');
    }
    
    // Transactions - buyers can create/read own, supervisors can read all
    match /transactions/{transactionId} {
      allow create: if request.auth != null && hasRole('buyer');
      allow read: if request.auth != null && 
        (resource.data.parties.buyerId == request.auth.uid || hasRole('supervisor'));
      allow update: if request.auth != null && hasRole('supervisor');
    }
    
    // Helper functions
    function hasRole(role) {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)/roles/$(role));
    }
    
    function canAccessCommodity(commodityId) {
      return exists(/databases/$(database)/documents/assignments/$(request.auth.uid + '_' + commodityId));
    }
  }
}
```

### 4.2 Google Cloud Storage Bucket Structure

#### 4.2.1 Bucket Organization
```
dcbuyer-user-files/
├── profiles/
│   └── {userId}/
│       ├── avatar.jpg
│       └── documents/
│           ├── id_document.pdf
│           └── certification.pdf
├── transactions/
│   └── {transactionId}/
│       ├── receipt.pdf
│       ├── quality_photos/
│       │   ├── sample_1.jpg
│       │   └── sample_2.jpg
│       └── signatures/
│           ├── buyer_signature.png
│           └── customer_signature.png
├── commodities/
│   └── {commodityId}/
│       ├── main_image.jpg
│       ├── gallery/
│       │   ├── image_1.jpg
│       │   └── image_2.jpg
│       └── specifications/
│           └── spec_document.pdf
├── customers/
│   └── {customerId}/
│       ├── business_license.pdf
│       ├── tax_certificate.pdf
│       └── contact_photos/
│           └── facility.jpg
└── system/
    ├── templates/
    │   ├── receipt_template.pdf
    │   └── contract_template.pdf
    └── backups/
        └── {date}/
            └── data_export.json
```

#### 4.2.2 File Naming Conventions
```dart
class FileNamingService {
  static String generateFileName(String fileType, String entityId, String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = originalName.split('.').last;
    return '${fileType}_${entityId}_${timestamp}.${extension}';
  }

  // Examples:
  // profile_user123_1642678800000.jpg
  // receipt_txn456_1642678800000.pdf
  // commodity_comm789_1642678800000.jpg
}
```

#### 4.2.3 Cloud Storage Security Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Profile images and documents
    match /profiles/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == userId || hasRole('admin'));
    }

    // Transaction receipts and photos
    match /transactions/{transactionId}/{allPaths=**} {
      allow read: if request.auth != null &&
        (canAccessTransaction(transactionId) || hasRole('supervisor'));
      allow write: if request.auth != null && hasRole('buyer');
    }

    // Commodity images and specifications
    match /commodities/{commodityId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && hasRole('admin');
    }

    // Customer documents
    match /customers/{customerId}/{allPaths=**} {
      allow read, write: if request.auth != null &&
        (hasRole('buyer') || hasRole('admin'));
    }

    // System files (templates, backups)
    match /system/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && hasRole('admin');
    }

    // Helper functions
    function hasRole(role) {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)/roles/$(role));
    }

    function canAccessTransaction(transactionId) {
      return exists(/databases/$(database)/documents/transactions/$(transactionId)) &&
        get(/databases/$(database)/documents/transactions/$(transactionId)).data.parties.buyerId == request.auth.uid;
    }
  }
}
```

## 5. Cloud Functions

### 5.1 Transaction Processing Functions
```typescript
// Auto-generate transaction numbers
export const generateTransactionNumber = functions.firestore
  .document('transactions/{transactionId}')
  .onCreate(async (snap, context) => {
    const data = snap.data();
    const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const counter = await getNextTransactionCounter(date);
    const transactionNumber = `DCB-${date}-${counter.toString().padStart(4, '0')}`;
    
    return snap.ref.update({ transactionNumber });
  });

// Send notifications on high-value transactions
export const notifyHighValueTransaction = functions.firestore
  .document('transactions/{transactionId}')
  .onCreate(async (snap, context) => {
    const data = snap.data();
    if (data.transaction.totalAmount > 5000) {
      // Send notification to supervisors
      await sendNotificationToRole('supervisor', {
        title: 'High Value Transaction',
        body: `Transaction ${data.transactionNumber} for $${data.transaction.totalAmount}`,
        data: { transactionId: context.params.transactionId }
      });
    }
  });
```

### 5.2 Data Validation Functions
```typescript
// Validate commodity assignments
export const validateCommodityAssignment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const { buyerId, commodityId } = data;
  
  // Check if buyer exists and has buyer role
  const buyerDoc = await admin.firestore().doc(`users/${buyerId}`).get();
  if (!buyerDoc.exists) {
    throw new functions.https.HttpsError('not-found', 'Buyer not found');
  }
  
  // Check if commodity exists and is active
  const commodityDoc = await admin.firestore().doc(`commodities/${commodityId}`).get();
  if (!commodityDoc.exists || !commodityDoc.data()?.status.isActive) {
    throw new functions.https.HttpsError('not-found', 'Commodity not found or inactive');
  }
  
  return { valid: true };
});
```

## 6. Offline-First Implementation

### 6.1 Local Storage Strategy
```dart
// SQLite for offline transactions
class OfflineTransactionService {
  static const String tableName = 'offline_transactions';
  
  Future<void> saveOfflineTransaction(Transaction transaction) async {
    final db = await DatabaseHelper.instance.database;
    await db.insert(tableName, transaction.toJson());
  }
  
  Future<List<Transaction>> getPendingTransactions() async {
    final db = await DatabaseHelper.instance.database;
    final maps = await db.query(tableName, where: 'is_synced = ?', whereArgs: [0]);
    return maps.map((map) => Transaction.fromJson(map)).toList();
  }
}
```

### 6.2 Sync Strategy
```dart
class FirebaseSyncService {
  Future<void> syncPendingTransactions() async {
    final offlineTransactions = await OfflineTransactionService().getPendingTransactions();
    
    for (final transaction in offlineTransactions) {
      try {
        // Upload to Firestore
        await FirebaseFirestore.instance
            .collection('transactions')
            .doc(transaction.id)
            .set(transaction.toFirestore());
            
        // Mark as synced locally
        await OfflineTransactionService().markAsSynced(transaction.id);
      } catch (e) {
        // Handle sync errors
        print('Failed to sync transaction ${transaction.id}: $e');
      }
    }
  }
  
  Future<void> enableOfflineSupport() async {
    await FirebaseFirestore.instance.enablePersistence();
  }
}
```

## 7. Real-time Features

### 7.1 Live Data Streams
```dart
class TransactionStreamService {
  Stream<List<Transaction>> getUserTransactions(String userId) {
    return FirebaseFirestore.instance
        .collection('transactions')
        .where('parties.buyerId', isEqualTo: userId)
        .orderBy('metadata.createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Transaction.fromFirestore(doc))
            .toList());
  }
  
  Stream<List<Commodity>> getAssignedCommodities(String buyerId) {
    return FirebaseFirestore.instance
        .collection('assignments')
        .where('buyerId', isEqualTo: buyerId)
        .where('assignment.isActive', isEqualTo: true)
        .snapshots()
        .asyncMap((snapshot) async {
          final commodityIds = snapshot.docs
              .map((doc) => doc.data()['commodityId'] as String)
              .toList();
              
          if (commodityIds.isEmpty) return <Commodity>[];
          
          final commodityDocs = await FirebaseFirestore.instance
              .collection('commodities')
              .where(FieldPath.documentId, whereIn: commodityIds)
              .get();
              
          return commodityDocs.docs
              .map((doc) => Commodity.fromFirestore(doc))
              .toList();
        });
  }
}
```

## 8. Performance Optimization

### 8.1 Firestore Optimization
- Use composite indexes for complex queries
- Implement pagination for large datasets
- Cache frequently accessed data locally
- Use Firestore offline persistence

### 8.2 File Upload and Management Service
```dart
class CloudStorageService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Upload user profile image
  Future<String> uploadProfileImage(String userId, File imageFile) async {
    final fileName = FileNamingService.generateFileName('profile', userId, imageFile.path);
    final path = 'profiles/$userId/$fileName';

    final compressedImage = await _compressImage(imageFile);
    final ref = _storage.ref().child(path);
    final uploadTask = ref.putData(compressedImage);

    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }

  // Upload transaction receipt
  Future<String> uploadTransactionReceipt(String transactionId, File receiptFile) async {
    final fileName = FileNamingService.generateFileName('receipt', transactionId, receiptFile.path);
    final path = 'transactions/$transactionId/$fileName';

    final ref = _storage.ref().child(path);
    final uploadTask = ref.putFile(receiptFile);

    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }

  // Upload commodity images
  Future<List<String>> uploadCommodityImages(String commodityId, List<File> imageFiles) async {
    final List<String> downloadUrls = [];

    for (int i = 0; i < imageFiles.length; i++) {
      final fileName = FileNamingService.generateFileName('commodity', commodityId, imageFiles[i].path);
      final path = 'commodities/$commodityId/gallery/$fileName';

      final compressedImage = await _compressImage(imageFiles[i]);
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putData(compressedImage);

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      downloadUrls.add(downloadUrl);
    }

    return downloadUrls;
  }

  // Upload customer documents
  Future<String> uploadCustomerDocument(String customerId, File documentFile, String documentType) async {
    final fileName = FileNamingService.generateFileName(documentType, customerId, documentFile.path);
    final path = 'customers/$customerId/$fileName';

    final ref = _storage.ref().child(path);
    final uploadTask = ref.putFile(documentFile);

    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }

  // Delete file from storage
  Future<void> deleteFile(String filePath) async {
    try {
      final ref = _storage.ref().child(filePath);
      await ref.delete();
    } catch (e) {
      print('Error deleting file: $e');
    }
  }

  // Get file metadata
  Future<FullMetadata> getFileMetadata(String filePath) async {
    final ref = _storage.ref().child(filePath);
    return await ref.getMetadata();
  }

  // Private method to compress images
  Future<Uint8List> _compressImage(File imageFile) async {
    final compressedImage = await FlutterImageCompress.compressWithFile(
      imageFile.absolute.path,
      quality: 80,
      minWidth: 800,
      minHeight: 600,
      format: CompressFormat.jpeg,
    );
    return compressedImage!;
  }
}
```

### 8.3 File Upload Progress Tracking
```dart
class FileUploadService {
  Stream<TaskSnapshot> uploadWithProgress(String path, File file) {
    final ref = FirebaseStorage.instance.ref().child(path);
    final uploadTask = ref.putFile(file);

    return uploadTask.snapshotEvents;
  }

  Future<String> uploadWithProgressCallback(
    String path,
    File file,
    Function(double progress) onProgress
  ) async {
    final ref = FirebaseStorage.instance.ref().child(path);
    final uploadTask = ref.putFile(file);

    uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
      final progress = snapshot.bytesTransferred / snapshot.totalBytes;
      onProgress(progress);
    });

    final snapshot = await uploadTask;
    return await snapshot.ref.getDownloadURL();
  }
}
```

## 9. Monitoring & Analytics

### 9.1 Firebase Analytics Events
```dart
class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  
  static Future<void> logTransactionCreated(Transaction transaction) async {
    await _analytics.logEvent(
      name: 'transaction_created',
      parameters: {
        'transaction_id': transaction.id,
        'commodity_id': transaction.commodity.commodityId,
        'amount': transaction.transaction.totalAmount,
        'currency': transaction.transaction.currency,
      },
    );
  }
  
  static Future<void> logUserLogin(String userId, String role) async {
    await _analytics.logLogin(loginMethod: 'firebase_auth');
    await _analytics.setUserProperty(name: 'user_role', value: role);
  }
}
```

### 9.2 Crashlytics Integration
```dart
class CrashlyticsService {
  static Future<void> initialize() async {
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }
  
  static Future<void> logError(String message, dynamic error, StackTrace? stackTrace) async {
    await FirebaseCrashlytics.instance.recordError(error, stackTrace, reason: message);
  }
}
```

## 10. Deployment Strategy

### 10.1 Firebase Project Setup
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project
firebase init

# Deploy Cloud Functions
firebase deploy --only functions

# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Storage rules
firebase deploy --only storage

# Deploy web app
firebase deploy --only hosting
```

### 10.2 Environment Configuration
```dart
class FirebaseConfig {
  static const String projectId = 'dcbuyer-prod';
  static const String storageBucket = 'dcbuyer-prod.appspot.com';
  
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError('DefaultFirebaseOptions are not supported for this platform.');
    }
  }
}
```

---

*This Firebase-based system design provides a serverless, scalable architecture for the DCBuyer application with real-time capabilities and offline-first functionality.*
