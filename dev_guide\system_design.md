# DCBuyer System Design Document

## 1. Overview

DCBuyer is an offline-first commodity trading and purchasing management system designed for agricultural marketplaces. The system enables role-based access control for commodity purchasing, customer management, and transaction recording with robust offline capabilities.

### 1.1 Purpose
- Streamline commodity purchasing processes
- Provide role-based access control for different user types
- Enable offline-first operations for field use
- Maintain comprehensive transaction records

### 1.2 Scope
The system covers user management, customer management, commodity management, buyer assignments, and transaction processing with offline synchronization capabilities.

## 2. System Architecture

### 2.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web App       │    │   Admin Panel   │
│   (Flutter)     │    │   (Flutter Web) │    │   (Flutter Web) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Firebase Services                  │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Firebase Auth│  │ Firestore   │  │Cloud Func││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │  GCS Bucket │  │ Analytics   │  │Messaging ││
         │  │(User Files) │  │             │  │          ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Local Storage                      │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   SQLite    │  │    Hive     │  │Local Files││
         │  │(Offline DB) │  │(Key-Value)  │  │(Images)  ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Frontend:**
- Flutter (Cross-platform mobile and web)
- Dart programming language
- Provider/Riverpod for state management
- Hive/SQLite for local storage

**Backend (Firebase):**
- Firebase Authentication for user management
- Cloud Firestore for NoSQL database
- Firebase Cloud Functions for server-side logic
- Google Cloud Storage for user-uploaded files (images, documents, receipts)
- Firebase Analytics for app analytics
- Firebase Cloud Messaging for push notifications

**Local Storage:**
- SQLite for offline data persistence
- Hive for key-value storage
- Shared Preferences for app settings

**File Storage:**
- Google Cloud Storage buckets for user-uploaded files:
  - Profile images and avatars
  - Transaction receipts and documents
  - Commodity photos and specifications
  - Customer documentation
  - Audit trail attachments

**Development & Deployment:**
- Firebase CLI for deployment
- GitHub Actions for CI/CD
- Firebase Hosting for web deployment

## 3. Firebase Database Design

### 3.1 Firestore Collections Structure
```
/users/{userId}
/roles/{roleId}
/userRoles/{userRoleId}
/customers/{customerId}
/commodities/{commodityId}
/buyerCommodityAssignments/{assignmentId}
/transactions/{transactionId}
/syncMetadata/{collectionName}
```

### 3.2 Firestore Document Schemas

#### Users Collection (`/users/{userId}`)
```json
{
  "userId": "auto-generated-id",
  "username": "string (unique)",
  "email": "string (unique)",
  "firstName": "string",
  "lastName": "string",
  "phone": "string",
  "isActive": true,
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "lastLogin": "timestamp",
  "syncVersion": 1,
  "profileImageUrl": "string (optional)"
}
```

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL, -- admin, buyer, supervisor, evaluator
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### UserRoles Table (Junction Table)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, role_id)
);
```

#### Customers Table
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(50) UNIQUE NOT NULL, -- Unique business identifier
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    location_coordinates POINT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1
);
```

#### Commodities Table
```sql
CREATE TABLE commodities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    unit_of_measurement VARCHAR(20) NOT NULL, -- kg, tons, bags, etc.
    category VARCHAR(50),
    current_price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1
);
```

#### BuyerCommodityAssignments Table
```sql
CREATE TABLE buyer_commodity_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    buyer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    commodity_id UUID REFERENCES commodities(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    max_purchase_limit DECIMAL(15,2),
    UNIQUE(buyer_id, commodity_id)
);
```

#### Transactions Table
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    buyer_id UUID REFERENCES users(id) NOT NULL,
    customer_id UUID REFERENCES customers(id) NOT NULL,
    commodity_id UUID REFERENCES commodities(id) NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    amount_paid DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(20), -- cash, bank_transfer, mobile_money
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    status VARCHAR(20) DEFAULT 'completed', -- pending, completed, cancelled
    location_coordinates POINT,
    is_synced BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1
);
```

## 4. System Components

### 4.1 Authentication & Authorization Module
- JWT-based authentication
- Role-based access control (RBAC)
- Multi-role support per user
- Session management
- Password reset functionality

### 4.2 User Management Module
- CRUD operations for users
- Role assignment/revocation
- User profile management
- Activity logging

### 4.3 Customer Management Module
- Customer registration and management
- Unique customer ID generation
- Contact information management
- Location tracking

### 4.4 Commodity Management Module
- Commodity catalog management
- Unit of measurement definitions
- Price management
- Category organization

### 4.5 Assignment Management Module
- Buyer-commodity assignment
- Assignment history tracking
- Bulk assignment operations
- Assignment validation

### 4.6 Transaction Processing Module
- Purchase transaction recording
- Real-time calculation
- Transaction validation
- Receipt generation

### 4.7 Offline Synchronization Module
- Local data storage
- Conflict resolution
- Background sync
- Data versioning
- Queue management for offline transactions

## 5. User Workflows

### 5.1 Buyer Workflow
1. **Login** → Authentication
2. **View Dashboard** → Display assigned commodities
3. **Select Commodity** → Choose from assigned list
4. **Select Customer** → Choose or create customer
5. **Enter Transaction Details** → Quantity, price, payment
6. **Confirm Transaction** → Validate and save
7. **Generate Receipt** → Print/share receipt

### 5.2 Admin Workflow
1. **Login** → Authentication
2. **User Management** → Create/edit users and roles
3. **Commodity Management** → Manage commodity catalog
4. **Assignment Management** → Assign commodities to buyers
5. **Reports & Analytics** → View system reports

### 5.3 Supervisor Workflow
1. **Login** → Authentication
2. **Transaction Monitoring** → View buyer transactions
3. **Performance Reports** → Analyze buyer performance
4. **Approval Workflows** → Approve high-value transactions

### 5.4 Evaluator Workflow
1. **Login** → Authentication
2. **Quality Assessment** → Record commodity quality
3. **Price Validation** → Verify transaction prices
4. **Audit Reports** → Generate audit trails

## 6. Offline-First Architecture

### 6.1 Local Storage Strategy
- **SQLite** for structured data
- **Hive** for key-value storage
- **File System** for documents/images

### 6.2 Synchronization Strategy
- **Incremental Sync** → Only sync changed data
- **Conflict Resolution** → Last-write-wins with manual resolution
- **Version Control** → Track data versions for conflict detection
- **Queue System** → Store offline transactions for later sync

### 6.3 Data Consistency
- **Optimistic Locking** → Prevent concurrent edit conflicts
- **Validation Rules** → Ensure data integrity
- **Rollback Mechanism** → Handle failed synchronizations

## 7. Security Considerations

### 7.1 Authentication Security
- Password hashing with bcrypt
- JWT token expiration
- Refresh token rotation
- Account lockout policies

### 7.2 Data Security
- Data encryption at rest
- TLS encryption in transit
- Input validation and sanitization
- SQL injection prevention

### 7.3 Access Control
- Role-based permissions
- Resource-level authorization
- Audit logging
- Session management

## 8. Performance Considerations

### 8.1 Database Optimization
- Proper indexing strategy
- Query optimization
- Connection pooling
- Read replicas for reporting

### 8.2 Caching Strategy
- Redis for session caching
- Application-level caching
- CDN for static assets
- Local caching for offline support

### 8.3 Mobile Performance
- Lazy loading
- Image optimization
- Background sync
- Battery optimization

## 9. Monitoring & Analytics

### 9.1 System Monitoring
- Application performance monitoring
- Database performance metrics
- Error tracking and logging
- Uptime monitoring

### 9.2 Business Analytics
- Transaction volume tracking
- User activity analytics
- Commodity performance metrics
- Revenue reporting

## 10. Deployment Architecture

### 10.1 Environment Strategy
- **Development** → Local development environment
- **Staging** → Pre-production testing
- **Production** → Live system deployment

### 10.2 Infrastructure
- **Load Balancer** → Distribute traffic
- **Application Servers** → Handle business logic
- **Database Cluster** → High availability data storage
- **Backup Systems** → Data protection and recovery

## 11. Google Cloud Storage Implementation

### 11.1 Bucket Structure and Organization
```
dcbuyer-user-files/
├── profiles/
│   └── {userId}/
│       ├── avatar.jpg
│       └── documents/
├── transactions/
│   └── {transactionId}/
│       ├── receipts/
│       ├── photos/
│       └── signatures/
├── commodities/
│   └── {commodityId}/
│       ├── images/
│       └── specifications/
├── customers/
│   └── {customerId}/
│       └── documents/
└── system/
    ├── templates/
    └── backups/
```

### 11.2 File Management Service
```python
# Example Python service for file management
class CloudStorageService:
    def __init__(self):
        self.client = storage.Client()
        self.bucket = self.client.bucket('dcbuyer-user-files')

    def upload_user_file(self, user_id: str, file_type: str, file_data: bytes, filename: str) -> str:
        """Upload user file to appropriate bucket path"""
        blob_name = f"{file_type}/{user_id}/{filename}"
        blob = self.bucket.blob(blob_name)
        blob.upload_from_string(file_data)
        return blob.public_url

    def delete_user_file(self, file_path: str) -> bool:
        """Delete file from storage"""
        blob = self.bucket.blob(file_path)
        blob.delete()
        return True

    def get_signed_url(self, file_path: str, expiration_hours: int = 1) -> str:
        """Generate signed URL for secure file access"""
        blob = self.bucket.blob(file_path)
        return blob.generate_signed_url(
            expiration=datetime.utcnow() + timedelta(hours=expiration_hours)
        )
```

### 11.3 File Security and Access Control
- **IAM Policies**: Role-based access to storage buckets
- **Signed URLs**: Temporary access to private files
- **File Encryption**: Server-side encryption for sensitive documents
- **Access Logging**: Audit trail for file operations
- **Virus Scanning**: Automated malware detection for uploads

## 12. Future Enhancements

### 12.1 Planned Features
- Real-time notifications
- Advanced reporting dashboard
- Mobile payment integration
- GPS tracking for transactions
- Machine learning for price prediction

### 12.2 Scalability Considerations
- Microservices architecture migration
- Horizontal scaling capabilities
- Multi-tenant support
- API rate limiting

---

*This document serves as the foundation for the DCBuyer system development and should be updated as requirements evolve.*
