# DCBuyer Relational Database System Design Document

## 1. Overview

DCBuyer is an offline-first commodity trading and purchasing management system built with a traditional relational database architecture. The system provides enterprise-grade data consistency, complex querying capabilities, and robust transaction management for agricultural marketplaces.

### 1.1 Purpose
- Streamline commodity purchasing processes with ACID compliance
- Provide role-based access control with relational integrity
- Enable offline-first operations with eventual consistency
- Maintain comprehensive audit trails and transaction records
- Support complex reporting and analytics queries

### 1.2 Scope
The system covers user management, customer management, commodity management, buyer assignments, and transaction processing with a traditional three-tier architecture using relational databases for data persistence.

### 1.3 Architecture Philosophy
- **ACID Compliance**: Ensure data consistency and integrity
- **Normalized Design**: Reduce data redundancy and maintain referential integrity
- **Scalable Backend**: Support horizontal and vertical scaling
- **API-First**: RESTful services for frontend consumption
- **Microservices Ready**: Modular design for future service separation

## 2. System Architecture

### 2.1 High-Level Architecture (Three-Tier Architecture)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web App       │    │   Admin Panel   │
│   (Flutter)     │    │   (Flutter Web) │    │   (Flutter Web) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │ HTTPS/REST API
         ┌─────────────────────────────────────────────────┐
         │              API Gateway & Load Balancer        │
         │                    (Nginx/HAProxy)              │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Application Layer                  │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Auth Service │  │Business API │  │File API  ││
         │  │(Node.js)    │  │(Node.js)    │  │(Node.js) ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Sync Service │  │Report API   │  │Notify API││
         │  │(Node.js)    │  │(Node.js)    │  │(Node.js) ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Data Layer                         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │PostgreSQL   │  │Redis Cache  │  │GCS Bucket││
         │  │(Primary DB) │  │(Session)    │  │(Files)   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Client Local Storage               │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   SQLite    │  │    Hive     │  │Local Files││
         │  │(Offline DB) │  │(Key-Value)  │  │(Cache)   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Frontend (Presentation Layer):**
- Flutter SDK (Cross-platform mobile and web)
- Dart programming language
- Provider/Riverpod for state management
- HTTP client for API communication
- SQLite for offline data persistence
- Hive for key-value caching
- Shared Preferences for app settings

**Backend (Application Layer):**
- **API Server**: Node.js with Express.js or Python with FastAPI
- **Authentication**: JWT tokens with refresh token rotation
- **Validation**: Joi (Node.js) or Pydantic (Python) for request validation
- **ORM**: Prisma (Node.js) or SQLAlchemy (Python)
- **Caching**: Redis for session management and query caching
- **Queue System**: Bull (Node.js) or Celery (Python) for background jobs
- **Real-time**: Socket.io or WebSockets for live updates

**Database (Data Layer):**
- **Primary Database**: PostgreSQL 14+ with JSONB support
- **Read Replicas**: PostgreSQL read replicas for reporting
- **Cache Layer**: Redis 6+ for session and query caching
- **Search Engine**: Elasticsearch for full-text search (optional)

**File Storage:**
- **Google Cloud Storage** for user-uploaded files:
  - Profile images and avatars
  - Transaction receipts and documents
  - Commodity photos and specifications
  - Customer business documents
  - Audit trail attachments

**Infrastructure & DevOps:**
- **Containerization**: Docker and Docker Compose
- **Orchestration**: Kubernetes or Docker Swarm
- **Load Balancer**: Nginx or HAProxy
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **CI/CD**: GitHub Actions or GitLab CI
- **Cloud Provider**: Google Cloud Platform or AWS

## 3. Relational Database Design

### 3.1 Entity Relationship Diagram
```
Users (1) ──────── (M) UserRoles (M) ──────── (1) Roles
  │                                              │
  │                                              │
  └── (1) ──────── (M) BuyerCommodityAssignments │
                    │                            │
                    └── (M) ──────── (1) Commodities
                                      │
                                      │
Customers (1) ──────── (M) Transactions (M) ──── (1) Commodities
                        │
                        │
                        └── (M) ──────── (1) Users (Buyer)

Additional Relationships:
- Users (1) ──────── (M) Transactions (Created By)
- Users (1) ──────── (M) Customers (Created By)
- Users (1) ──────── (M) AuditLogs (Actor)
- Transactions (1) ──────── (M) TransactionFiles
- Customers (1) ──────── (M) CustomerFiles
- Commodities (1) ──────── (M) CommodityFiles
```

### 3.2 PostgreSQL Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    profile_image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1,

    -- Indexes
    CONSTRAINT users_username_check CHECK (length(username) >= 3),
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL, -- admin, buyer, supervisor, evaluator
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT roles_name_check CHECK (name ~ '^[a-z_]+$')
);

-- Insert default roles
INSERT INTO roles (name, display_name, description, permissions, is_system_role) VALUES
('admin', 'Administrator', 'Full system access', '["*"]', true),
('buyer', 'Buyer', 'Can purchase commodities', '["read_commodities", "create_transactions", "read_customers", "create_customers"]', true),
('supervisor', 'Supervisor', 'Can supervise buyers and approve transactions', '["read_all_transactions", "approve_transactions", "read_reports"]', true),
('evaluator', 'Evaluator', 'Can evaluate commodity quality', '["read_commodities", "create_evaluations", "read_transactions"]', true);

CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_active ON roles(is_active);
```

#### UserRoles Table (Junction Table)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id),
    expires_at TIMESTAMP, -- Optional role expiration
    is_active BOOLEAN DEFAULT true,
    notes TEXT,

    UNIQUE(user_id, role_id)
);

CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_user_roles_active ON user_roles(is_active);
```

#### Customers Table
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id VARCHAR(50) UNIQUE NOT NULL, -- Unique business identifier
    business_name VARCHAR(200) NOT NULL,
    contact_person_name VARCHAR(100),
    contact_person_title VARCHAR(50),
    primary_phone VARCHAR(20),
    secondary_phone VARCHAR(20),
    email VARCHAR(100),
    business_registration_number VARCHAR(50),
    tax_identification_number VARCHAR(50),

    -- Address information
    address_line_1 TEXT,
    address_line_2 TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Ghana',

    -- Geographic coordinates
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),

    -- Business information
    business_type VARCHAR(50), -- farm, cooperative, trader, etc.
    established_date DATE,
    credit_limit DECIMAL(15, 2) DEFAULT 0,
    payment_terms VARCHAR(50) DEFAULT 'cash', -- cash, net-30, net-60

    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP,
    verified_by UUID REFERENCES users(id),
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1,

    -- Constraints
    CONSTRAINT customers_customer_id_check CHECK (length(customer_id) >= 3),
    CONSTRAINT customers_business_name_check CHECK (length(business_name) >= 2),
    CONSTRAINT customers_coordinates_check CHECK (
        (latitude IS NULL AND longitude IS NULL) OR
        (latitude IS NOT NULL AND longitude IS NOT NULL)
    )
);

-- Indexes for performance
CREATE INDEX idx_customers_customer_id ON customers(customer_id);
CREATE INDEX idx_customers_business_name ON customers(business_name);
CREATE INDEX idx_customers_active ON customers(is_active);
CREATE INDEX idx_customers_created_by ON customers(created_by);
CREATE INDEX idx_customers_location ON customers USING GIST (point(longitude, latitude));
CREATE INDEX idx_customers_created_at ON customers(created_at);
```

#### Commodities Table
```sql
CREATE TABLE commodities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    commodity_code VARCHAR(20) UNIQUE NOT NULL, -- MAIZE-001, RICE-002
    name VARCHAR(100) NOT NULL,
    local_name VARCHAR(100), -- Local language name
    description TEXT,
    category VARCHAR(50) NOT NULL, -- grains, legumes, vegetables, etc.
    subcategory VARCHAR(50),

    -- Measurement and pricing
    primary_unit VARCHAR(20) NOT NULL, -- kg, tons, bags, liters
    secondary_unit VARCHAR(20), -- Alternative unit
    conversion_factor DECIMAL(10, 4), -- Conversion between primary and secondary
    base_price DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'GHS', -- Ghana Cedis
    price_per_unit DECIMAL(10, 4),

    -- Quality specifications
    grade VARCHAR(10), -- A, B, C, Premium
    quality_standards JSONB, -- Flexible quality criteria
    moisture_content_max DECIMAL(5, 2), -- Maximum moisture percentage
    purity_min DECIMAL(5, 2), -- Minimum purity percentage

    -- Seasonal information
    harvest_season_start INTEGER, -- Month (1-12)
    harvest_season_end INTEGER, -- Month (1-12)
    storage_life_days INTEGER, -- Days commodity can be stored

    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    is_seasonal BOOLEAN DEFAULT false,
    requires_quality_check BOOLEAN DEFAULT true,
    minimum_purchase_quantity DECIMAL(10, 3),
    maximum_purchase_quantity DECIMAL(10, 3),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1,

    -- Constraints
    CONSTRAINT commodities_name_check CHECK (length(name) >= 2),
    CONSTRAINT commodities_price_check CHECK (base_price >= 0),
    CONSTRAINT commodities_season_check CHECK (
        (harvest_season_start IS NULL AND harvest_season_end IS NULL) OR
        (harvest_season_start BETWEEN 1 AND 12 AND harvest_season_end BETWEEN 1 AND 12)
    )
);

-- Indexes for performance
CREATE INDEX idx_commodities_code ON commodities(commodity_code);
CREATE INDEX idx_commodities_name ON commodities(name);
CREATE INDEX idx_commodities_category ON commodities(category);
CREATE INDEX idx_commodities_active ON commodities(is_active);
CREATE INDEX idx_commodities_seasonal ON commodities(is_seasonal);
CREATE INDEX idx_commodities_created_at ON commodities(created_at);
```

#### BuyerCommodityAssignments Table
```sql
CREATE TABLE buyer_commodity_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    buyer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    commodity_id UUID NOT NULL REFERENCES commodities(id) ON DELETE CASCADE,

    -- Assignment details
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID NOT NULL REFERENCES users(id),
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE, -- Optional end date
    is_active BOOLEAN DEFAULT true,

    -- Purchase limits
    daily_purchase_limit DECIMAL(15, 2),
    weekly_purchase_limit DECIMAL(15, 2),
    monthly_purchase_limit DECIMAL(15, 2),
    max_transaction_amount DECIMAL(15, 2),
    min_transaction_amount DECIMAL(15, 2),

    -- Territory and restrictions
    territory VARCHAR(100), -- Geographic area
    allowed_customers JSONB, -- Specific customer IDs if restricted
    restricted_customers JSONB, -- Customers not allowed

    -- Performance tracking
    total_purchases_amount DECIMAL(15, 2) DEFAULT 0,
    total_transactions_count INTEGER DEFAULT 0,
    last_transaction_date TIMESTAMP,

    -- Metadata
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,

    UNIQUE(buyer_id, commodity_id, effective_from)
);

-- Indexes for performance
CREATE INDEX idx_assignments_buyer_id ON buyer_commodity_assignments(buyer_id);
CREATE INDEX idx_assignments_commodity_id ON buyer_commodity_assignments(commodity_id);
CREATE INDEX idx_assignments_active ON buyer_commodity_assignments(is_active);
CREATE INDEX idx_assignments_effective ON buyer_commodity_assignments(effective_from, effective_to);
CREATE INDEX idx_assignments_assigned_by ON buyer_commodity_assignments(assigned_by);
```

#### Transactions Table
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- Parties involved
    buyer_id UUID NOT NULL REFERENCES users(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    commodity_id UUID NOT NULL REFERENCES commodities(id),

    -- Transaction details
    quantity DECIMAL(12, 4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(12, 4) NOT NULL,
    subtotal DECIMAL(15, 2) NOT NULL,
    tax_amount DECIMAL(15, 2) DEFAULT 0,
    discount_amount DECIMAL(15, 2) DEFAULT 0,
    total_amount DECIMAL(15, 2) NOT NULL,

    -- Payment information
    amount_paid DECIMAL(15, 2) NOT NULL,
    balance_due DECIMAL(15, 2) DEFAULT 0,
    payment_method VARCHAR(30), -- cash, bank_transfer, mobile_money, credit
    payment_reference VARCHAR(100), -- Reference number for electronic payments
    payment_status VARCHAR(20) DEFAULT 'completed', -- pending, completed, partial, failed

    -- Quality and grading
    quality_grade VARCHAR(10),
    quality_notes TEXT,
    moisture_content DECIMAL(5, 2),
    purity_percentage DECIMAL(5, 2),
    quality_checked_by UUID REFERENCES users(id),
    quality_checked_at TIMESTAMP,

    -- Location and timing
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_name VARCHAR(200),

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft', -- draft, pending, approved, completed, cancelled, disputed
    approval_status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    rejection_reason TEXT,

    -- Delivery information
    delivery_date DATE,
    delivery_address TEXT,
    delivery_status VARCHAR(20), -- pending, in_transit, delivered, failed
    delivery_notes TEXT,

    -- Financial tracking
    commission_rate DECIMAL(5, 4), -- Percentage
    commission_amount DECIMAL(15, 2),
    net_amount DECIMAL(15, 2), -- Amount after commission

    -- Audit and sync
    notes TEXT,
    internal_notes TEXT, -- Private notes for staff
    is_synced BOOLEAN DEFAULT false,
    synced_at TIMESTAMP,
    device_id VARCHAR(100), -- Device that created the transaction
    app_version VARCHAR(20),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1,

    -- Constraints
    CONSTRAINT transactions_quantity_positive CHECK (quantity > 0),
    CONSTRAINT transactions_unit_price_positive CHECK (unit_price > 0),
    CONSTRAINT transactions_total_positive CHECK (total_amount > 0),
    CONSTRAINT transactions_amount_paid_non_negative CHECK (amount_paid >= 0),
    CONSTRAINT transactions_coordinates_check CHECK (
        (latitude IS NULL AND longitude IS NULL) OR
        (latitude IS NOT NULL AND longitude IS NOT NULL)
    )
);

-- Indexes for performance
CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_buyer_id ON transactions(buyer_id);
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_commodity_id ON transactions(commodity_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_approval_status ON transactions(approval_status);
CREATE INDEX idx_transactions_payment_status ON transactions(payment_status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_location ON transactions USING GIST (point(longitude, latitude));
CREATE INDEX idx_transactions_sync ON transactions(is_synced);
```

#### Supporting Tables

##### File Attachments Table
```sql
CREATE TABLE file_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL, -- transaction, customer, commodity, user
    entity_id UUID NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- receipt, photo, document, signature
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    gcs_bucket VARCHAR(100) NOT NULL,
    gcs_path TEXT NOT NULL,
    gcs_url TEXT NOT NULL,

    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- File processing
    is_processed BOOLEAN DEFAULT false,
    processing_status VARCHAR(20), -- pending, processing, completed, failed
    thumbnail_url TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_file_attachments_entity ON file_attachments(entity_type, entity_id);
CREATE INDEX idx_file_attachments_type ON file_attachments(file_type);
CREATE INDEX idx_file_attachments_uploaded_by ON file_attachments(uploaded_by);
```

##### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[], -- Array of changed field names

    -- Actor information
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(100),
    user_role VARCHAR(50),

    -- Request context
    ip_address INET,
    user_agent TEXT,
    request_id UUID,
    session_id VARCHAR(100),

    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Additional context
    reason TEXT, -- Why the change was made
    notes TEXT
);

CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

##### Sync Metadata Table
```sql
CREATE TABLE sync_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    last_sync_timestamp TIMESTAMP NOT NULL,
    sync_status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, failed
    records_synced INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    error_message TEXT,
    device_id VARCHAR(100),
    user_id UUID REFERENCES users(id),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(table_name, device_id)
);

CREATE INDEX idx_sync_metadata_table ON sync_metadata(table_name);
CREATE INDEX idx_sync_metadata_device ON sync_metadata(device_id);
CREATE INDEX idx_sync_metadata_status ON sync_metadata(sync_status);
```

## 4. System Components

### 4.1 Authentication & Authorization Service
**Technology**: Node.js/Express with JWT
- **JWT-based authentication** with access and refresh tokens
- **Role-based access control (RBAC)** with permission checking
- **Multi-role support** per user with role hierarchy
- **Session management** with Redis for token blacklisting
- **Password security** with bcrypt hashing and complexity requirements
- **Account security** with login attempt limiting and account lockout
- **Password reset** with secure token-based flow

### 4.2 User Management Service
**Technology**: RESTful API with PostgreSQL
- **CRUD operations** for users with validation and sanitization
- **Role assignment/revocation** with audit logging
- **User profile management** including profile image uploads
- **Activity logging** and session tracking
- **User search and filtering** with pagination
- **Bulk user operations** for administrative tasks

### 4.3 Customer Management Service
**Technology**: RESTful API with PostgreSQL + GCS
- **Customer registration** with business validation
- **Unique customer ID generation** with configurable patterns
- **Contact information management** with verification workflows
- **Geographic location tracking** with coordinate validation
- **Document management** with GCS integration
- **Customer search** with full-text search capabilities
- **Credit limit management** and payment terms

### 4.4 Commodity Management Service
**Technology**: RESTful API with PostgreSQL + GCS
- **Commodity catalog management** with hierarchical categories
- **Unit of measurement** definitions and conversions
- **Dynamic pricing** with historical price tracking
- **Quality specifications** with JSONB flexible schema
- **Seasonal availability** tracking and alerts
- **Image gallery management** with GCS integration
- **Inventory tracking** and availability status

### 4.5 Assignment Management Service
**Technology**: RESTful API with PostgreSQL
- **Buyer-commodity assignment** with territory restrictions
- **Assignment history tracking** with effective date ranges
- **Bulk assignment operations** with validation
- **Purchase limit management** (daily, weekly, monthly)
- **Performance tracking** and analytics
- **Assignment validation** against business rules

### 4.6 Transaction Processing Service
**Technology**: RESTful API with PostgreSQL + Redis
- **Transaction creation** with real-time validation
- **Multi-step workflow** (draft → pending → approved → completed)
- **Payment processing** with multiple payment methods
- **Quality assessment** integration with grading
- **Receipt generation** with PDF templates
- **Transaction approval** workflow for supervisors
- **Commission calculation** and financial tracking

### 4.7 File Management Service
**Technology**: Node.js with Google Cloud Storage
- **File upload** with validation and virus scanning
- **Image optimization** and thumbnail generation
- **Secure file access** with signed URLs
- **File organization** by entity type and ID
- **Metadata management** with database tracking
- **File cleanup** and lifecycle management

### 4.8 Offline Synchronization Service
**Technology**: RESTful API with conflict resolution
- **Local SQLite storage** for offline operations
- **Incremental sync** with timestamp-based changes
- **Conflict resolution** with last-write-wins and manual resolution
- **Data versioning** for consistency checking
- **Queue management** for offline transactions
- **Background sync** with retry mechanisms
- **Sync status tracking** and error handling

### 4.9 Reporting & Analytics Service
**Technology**: PostgreSQL with read replicas
- **Transaction reports** with filtering and grouping
- **Performance analytics** for buyers and commodities
- **Financial reports** with commission tracking
- **Data export** in multiple formats (PDF, Excel, CSV)
- **Dashboard metrics** with real-time updates
- **Custom report builder** for administrators

## 5. RESTful API Design

### 5.1 API Architecture Principles
- **RESTful design** with standard HTTP methods
- **Resource-based URLs** with consistent naming
- **JSON request/response** format
- **Stateless authentication** with JWT tokens
- **Versioning** through URL path (/api/v1/)
- **Pagination** for list endpoints
- **Error handling** with standard HTTP status codes
- **Rate limiting** to prevent abuse

### 5.2 Core API Endpoints

#### Authentication Endpoints
```
POST   /api/v1/auth/login              # User login
POST   /api/v1/auth/logout             # User logout
POST   /api/v1/auth/refresh            # Refresh access token
POST   /api/v1/auth/forgot-password    # Request password reset
POST   /api/v1/auth/reset-password     # Reset password with token
GET    /api/v1/auth/me                 # Get current user info
```

#### User Management Endpoints
```
GET    /api/v1/users                   # List users (paginated)
POST   /api/v1/users                   # Create new user
GET    /api/v1/users/:id               # Get user by ID
PUT    /api/v1/users/:id               # Update user
DELETE /api/v1/users/:id               # Deactivate user
GET    /api/v1/users/:id/roles         # Get user roles
POST   /api/v1/users/:id/roles         # Assign role to user
DELETE /api/v1/users/:id/roles/:roleId # Remove role from user
```

#### Customer Management Endpoints
```
GET    /api/v1/customers               # List customers (paginated)
POST   /api/v1/customers               # Create new customer
GET    /api/v1/customers/:id           # Get customer by ID
PUT    /api/v1/customers/:id           # Update customer
DELETE /api/v1/customers/:id           # Deactivate customer
GET    /api/v1/customers/search        # Search customers
POST   /api/v1/customers/:id/files     # Upload customer documents
GET    /api/v1/customers/:id/files     # List customer files
```

#### Commodity Management Endpoints
```
GET    /api/v1/commodities             # List commodities (paginated)
POST   /api/v1/commodities             # Create new commodity
GET    /api/v1/commodities/:id         # Get commodity by ID
PUT    /api/v1/commodities/:id         # Update commodity
DELETE /api/v1/commodities/:id         # Deactivate commodity
GET    /api/v1/commodities/categories  # List commodity categories
POST   /api/v1/commodities/:id/images  # Upload commodity images
```

#### Assignment Management Endpoints
```
GET    /api/v1/assignments             # List assignments (paginated)
POST   /api/v1/assignments             # Create new assignment
GET    /api/v1/assignments/:id         # Get assignment by ID
PUT    /api/v1/assignments/:id         # Update assignment
DELETE /api/v1/assignments/:id         # Deactivate assignment
GET    /api/v1/buyers/:id/commodities  # Get buyer's assigned commodities
```

#### Transaction Management Endpoints
```
GET    /api/v1/transactions            # List transactions (paginated)
POST   /api/v1/transactions            # Create new transaction
GET    /api/v1/transactions/:id        # Get transaction by ID
PUT    /api/v1/transactions/:id        # Update transaction
DELETE /api/v1/transactions/:id        # Cancel transaction
POST   /api/v1/transactions/:id/approve # Approve transaction
POST   /api/v1/transactions/:id/reject  # Reject transaction
GET    /api/v1/transactions/:id/receipt # Generate receipt PDF
POST   /api/v1/transactions/:id/files   # Upload transaction files
```

#### File Management Endpoints
```
POST   /api/v1/files/upload            # Upload file
GET    /api/v1/files/:id               # Get file metadata
DELETE /api/v1/files/:id               # Delete file
GET    /api/v1/files/:id/download      # Download file (signed URL)
```

#### Sync Endpoints
```
GET    /api/v1/sync/status             # Get sync status
POST   /api/v1/sync/pull               # Pull changes from server
POST   /api/v1/sync/push               # Push changes to server
GET    /api/v1/sync/conflicts          # Get sync conflicts
POST   /api/v1/sync/resolve            # Resolve sync conflicts
```

### 5.3 API Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### 5.4 Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

## 6. User Workflows

### 5.1 Buyer Workflow
1. **Login** → Authentication
2. **View Dashboard** → Display assigned commodities
3. **Select Commodity** → Choose from assigned list
4. **Select Customer** → Choose or create customer
5. **Enter Transaction Details** → Quantity, price, payment
6. **Confirm Transaction** → Validate and save
7. **Generate Receipt** → Print/share receipt

### 5.2 Admin Workflow
1. **Login** → Authentication
2. **User Management** → Create/edit users and roles
3. **Commodity Management** → Manage commodity catalog
4. **Assignment Management** → Assign commodities to buyers
5. **Reports & Analytics** → View system reports

### 5.3 Supervisor Workflow
1. **Login** → Authentication
2. **Transaction Monitoring** → View buyer transactions
3. **Performance Reports** → Analyze buyer performance
4. **Approval Workflows** → Approve high-value transactions

### 5.4 Evaluator Workflow
1. **Login** → Authentication
2. **Quality Assessment** → Record commodity quality
3. **Price Validation** → Verify transaction prices
4. **Audit Reports** → Generate audit trails

## 6. Offline-First Architecture

### 6.1 Local Storage Strategy
- **SQLite** for structured data
- **Hive** for key-value storage
- **File System** for documents/images

### 6.2 Synchronization Strategy
- **Incremental Sync** → Only sync changed data
- **Conflict Resolution** → Last-write-wins with manual resolution
- **Version Control** → Track data versions for conflict detection
- **Queue System** → Store offline transactions for later sync

### 6.3 Data Consistency
- **Optimistic Locking** → Prevent concurrent edit conflicts
- **Validation Rules** → Ensure data integrity
- **Rollback Mechanism** → Handle failed synchronizations

## 7. Security Considerations

### 7.1 Authentication Security
- Password hashing with bcrypt
- JWT token expiration
- Refresh token rotation
- Account lockout policies

### 7.2 Data Security
- Data encryption at rest
- TLS encryption in transit
- Input validation and sanitization
- SQL injection prevention

### 7.3 Access Control
- Role-based permissions
- Resource-level authorization
- Audit logging
- Session management

## 8. Performance Considerations

### 8.1 Database Optimization
- Proper indexing strategy
- Query optimization
- Connection pooling
- Read replicas for reporting

### 8.2 Caching Strategy
- Redis for session caching
- Application-level caching
- CDN for static assets
- Local caching for offline support

### 8.3 Mobile Performance
- Lazy loading
- Image optimization
- Background sync
- Battery optimization

## 9. Monitoring & Analytics

### 9.1 System Monitoring
- Application performance monitoring
- Database performance metrics
- Error tracking and logging
- Uptime monitoring

### 9.2 Business Analytics
- Transaction volume tracking
- User activity analytics
- Commodity performance metrics
- Revenue reporting

## 10. Deployment Architecture

### 10.1 Environment Strategy
- **Development** → Local development environment
- **Staging** → Pre-production testing
- **Production** → Live system deployment

### 10.2 Infrastructure
- **Load Balancer** → Distribute traffic
- **Application Servers** → Handle business logic
- **Database Cluster** → High availability data storage
- **Backup Systems** → Data protection and recovery

## 11. Google Cloud Storage Implementation

### 11.1 Bucket Structure and Organization
```
dcbuyer-user-files/
├── profiles/
│   └── {userId}/
│       ├── avatar.jpg
│       └── documents/
├── transactions/
│   └── {transactionId}/
│       ├── receipts/
│       ├── photos/
│       └── signatures/
├── commodities/
│   └── {commodityId}/
│       ├── images/
│       └── specifications/
├── customers/
│   └── {customerId}/
│       └── documents/
└── system/
    ├── templates/
    └── backups/
```

### 11.2 File Management Service
```python
# Example Python service for file management
class CloudStorageService:
    def __init__(self):
        self.client = storage.Client()
        self.bucket = self.client.bucket('dcbuyer-user-files')

    def upload_user_file(self, user_id: str, file_type: str, file_data: bytes, filename: str) -> str:
        """Upload user file to appropriate bucket path"""
        blob_name = f"{file_type}/{user_id}/{filename}"
        blob = self.bucket.blob(blob_name)
        blob.upload_from_string(file_data)
        return blob.public_url

    def delete_user_file(self, file_path: str) -> bool:
        """Delete file from storage"""
        blob = self.bucket.blob(file_path)
        blob.delete()
        return True

    def get_signed_url(self, file_path: str, expiration_hours: int = 1) -> str:
        """Generate signed URL for secure file access"""
        blob = self.bucket.blob(file_path)
        return blob.generate_signed_url(
            expiration=datetime.utcnow() + timedelta(hours=expiration_hours)
        )
```

### 11.3 File Security and Access Control
- **IAM Policies**: Role-based access to storage buckets
- **Signed URLs**: Temporary access to private files
- **File Encryption**: Server-side encryption for sensitive documents
- **Access Logging**: Audit trail for file operations
- **Virus Scanning**: Automated malware detection for uploads

## 12. Future Enhancements

### 12.1 Planned Features
- Real-time notifications
- Advanced reporting dashboard
- Mobile payment integration
- GPS tracking for transactions
- Machine learning for price prediction

### 12.2 Scalability Considerations
- Microservices architecture migration
- Horizontal scaling capabilities
- Multi-tenant support
- API rate limiting

---

*This document serves as the foundation for the DCBuyer system development and should be updated as requirements evolve.*
